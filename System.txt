👥 User Roles:
    1. Student
        ◦ Login (With Matricule and password)
        ◦ Password recovery through email or phone number (This will be gotten from the information in the registration form)
        ◦ During the registration process, when the student submits the form, a verification code will be sent either to his phone number or to the email address, depending on the medium the student chooses.
        ◦ File new complaints
        ◦ Track complaint status (Unresolved, Processing, Resolve, Rejected)
        ◦ Receive updates (through email with link to uba website to login and view updated transcript)
        ◦ Give feedback
    2. Admin (School management)
        ◦ Manage user accounts (Block student accounts, Create departments/department accounts)
        ◦ Login (With Email/Username and Password)
        ◦ Set Datelines for submissions
        ◦ View all complaints in the system
        ◦ Monitor resolution process
        ◦ Generate reports 
        The admin login page should be accessed via a special route. That is, I do not want a visible button that leads to the admin login page, instead, I should accessed it directly using the URL
    3. Department Officer (Each department has a department officer)
        ◦ View complaints submitted to the respective department.(This is chosen on the complaint form and the when the student submits the complaint, it automatically goes to the department chosen on the form)
        ◦ Login (With Email/Username and Password)
        ◦ System sends a message to the Department officer’s email, at a particular time of the day let’s say (5:00pm), about the number of complaints which have been submitted for that day
        ◦ Respond and update status
        ◦ Communicate with students
        The Department Officer login page should be accessed via a special route. That is, I do not want a visible button that leads to the Department login page, instead, I should accessed it directly using the URL

🧩 Key Modules & Features:
1. User Authentication & Access Control
    • Login/Logout/Register
    • Role-based dashboard
2. Complaint Submission
    • Complaint category (e.g. CA, Exam, etc)
    • Description field
    • File attachments (optional)
    • Submission timestamp
3. Complaint Tracking & Status
    • Status: Pending, In Progress, Resolved, Rejected
    • Automatic updates via dashboard/email
4. Admin Panel
    • View and filter all complaints
    • Manage User accounts
5. Department/Staff Interface
    • View complaints
    • Respond with resolution notes
    • Change complaint status
6. Notifications System
    • Email/SMS/On-site alerts for updates
    • New complaint alerts to admin/staff
7. Feedback & Satisfaction
    • Student rates resolution quality
    • Optional comment for feedback
    • View resolutions
8. Reports & Analytics
    • Monthly/quarterly/yearly reports
    • Complaint resolution time
    • Staff performance metrics

🛠️ Technologies You Can Use:
Layer
Suggested Tech Stack
Frontend: HTML, tailwind CSS,React with vite
Backend:Supabase 

COMPLAINT FORM
Name of Student:……………………………………………………….	
Registration Number:……………………………...	Year of Study:……………………
Department:………………………………… Date:……………………
Phone Number:…………………………………………………….
Academic Year:…………………………………… Semester Concerned:………………... 
Course Code:……………………………………………… Course Level:………………...  
Course Title:…………………………………………………………..
Complaint Concerning 	[checkbox]CA Mark 	[checkbox]Exam Mark     [checkbox]Other 
Description: …………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………….


Use this color code: #08387F and white color

1. The name of the organization is NAPHI, and I want the application name to be NAPHI Complaints. For the logo, Just use NC
2. The departments are:
    -Centre for Cybersecurity and Mathematical Cryptology - CMC
    -Chemical and Biological Engineering - CBE
    -Civil Engineering and Architecture - CVL
    -Computer Engineering  - COM
    -Electrical and Electronics Engineering - EEEE
    -Mechanical and Industrial Engineering - MEC
    -Mining and Mineral Engineering - MIN
    -Petroleum Engineering - PET
3. I have an email service, it's <EMAIL>
4. The complaint categories are:
    -CA Mark
    -Exam Mark
    -Other
5. The file types should be allowed are:
    -PDF
    -Images
    -Documents
6. The typical "Years of Study" are:
    -1st Year
    -2nd Year
    -3rd Year
    -4th Year
   The semester options are:
    -1st Semester
    -2nd Semester
7. I want to host this application on Vercel.

Remember to take into consideration mobile responsiveness


Continue with authentication integration (connecting forms to Supabase)
Help you set up Supabase first (I can guide you through it)
Show you how to test the current interface (registration, login flows)

Continue building the student dashboard and complaint system?
Test the current authentication first to make sure everything works?
Show you how to create admin/department users in Supabase?
