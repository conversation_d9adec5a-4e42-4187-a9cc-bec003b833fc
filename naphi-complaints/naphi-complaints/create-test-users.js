import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTestUsers() {
  console.log('🚀 Creating test users for NAPHI Complaints System...\n')

  try {
    // 1. Create Student User
    console.log('👨‍🎓 Creating Student User...')
    const { data: studentAuth, error: studentAuthError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'student123',
      email_confirm: true,
      user_metadata: {
        full_name: '<PERSON>',
        user_type: 'student'
      }
    })

    if (studentAuthError) {
      console.error('❌ Student auth creation failed:', studentAuthError.message)
    } else {
      console.log('✅ Student auth user created:', studentAuth.user.email)
      
      // Create student profile
      const { data: studentProfile, error: studentProfileError } = await supabase
        .from('students')
        .insert([{
          user_id: studentAuth.user.id,
          full_name: 'John Doe Student',
          matricule: 'UBa25A1001',
          email: '<EMAIL>',
          phone_number: '+1234567890',
          department_code: 'COM',
          year_of_study: '2nd Year',
          verification_method: 'email',
          is_verified: true
        }])
        .select()

      if (studentProfileError) {
        console.error('❌ Student profile creation failed:', studentProfileError.message)
      } else {
        console.log('✅ Student profile created successfully')
      }
    }

    // 2. Create Admin User
    console.log('\n👑 Creating Admin User...')
    const { data: adminAuth, error: adminAuthError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      email_confirm: true,
      user_metadata: {
        full_name: 'System Administrator',
        user_type: 'admin'
      }
    })

    if (adminAuthError) {
      console.error('❌ Admin auth creation failed:', adminAuthError.message)
    } else {
      console.log('✅ Admin auth user created:', adminAuth.user.email)
      
      // Create admin profile
      const { data: adminProfile, error: adminProfileError } = await supabase
        .from('admins')
        .insert([{
          user_id: adminAuth.user.id,
          full_name: 'System Administrator',
          email: '<EMAIL>',
          is_super_admin: true,
          is_active: true
        }])
        .select()

      if (adminProfileError) {
        console.error('❌ Admin profile creation failed:', adminProfileError.message)
      } else {
        console.log('✅ Admin profile created successfully')
      }
    }

    // 3. Create Department Officer User
    console.log('\n🏢 Creating Department Officer User...')
    const { data: deptAuth, error: deptAuthError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'dept123',
      email_confirm: true,
      user_metadata: {
        full_name: 'CMC Department Officer',
        user_type: 'department_officer'
      }
    })

    if (deptAuthError) {
      console.error('❌ Department officer auth creation failed:', deptAuthError.message)
    } else {
      console.log('✅ Department officer auth user created:', deptAuth.user.email)
      
      // Create department officer profile
      const { data: deptProfile, error: deptProfileError } = await supabase
        .from('department_officers')
        .insert([{
          user_id: deptAuth.user.id,
          full_name: 'CMC Department Officer',
          email: '<EMAIL>',
          department_code: 'CMC',
          is_active: true
        }])
        .select()

      if (deptProfileError) {
        console.error('❌ Department officer profile creation failed:', deptProfileError.message)
      } else {
        console.log('✅ Department officer profile created successfully')
      }
    }

    console.log('\n🎉 Test user creation completed!')
    console.log('\n📋 Test Credentials:')
    console.log('👨‍🎓 Student Login:')
    console.log('   Matricule: UBa25A1001')
    console.log('   Password: student123')
    console.log('   URL: http://localhost:5175/student/login')
    
    console.log('\n👑 Admin Login:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')
    console.log('   URL: http://localhost:5175/admin/login')
    
    console.log('\n🏢 Department Officer Login:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: dept123')
    console.log('   URL: http://localhost:5175/department/login')

  } catch (error) {
    console.error('💥 Unexpected error:', error.message)
  }
}

// Run the script
createTestUsers().then(() => {
  console.log('\n✨ Script completed. You can now test the authentication system!')
  process.exit(0)
}).catch((error) => {
  console.error('💥 Script failed:', error.message)
  process.exit(1)
})
