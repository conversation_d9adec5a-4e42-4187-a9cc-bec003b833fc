import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createUserProfiles() {
  console.log('👥 Creating user profiles with service role key...\n')

  try {
    // Get all auth users
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
    
    if (usersError) {
      console.error('❌ Failed to get users:', usersError.message)
      return
    }

    console.log(`📋 Found ${users.users.length} auth users`)

    // Find our test users
    const studentUser = users.users.find(u => u.email === '<EMAIL>')
    const deptUser = users.users.find(u => u.email === '<EMAIL>')
    const adminUser = users.users.find(u => u.email === '<EMAIL>')

    // Create student profile
    if (studentUser) {
      console.log('👨‍🎓 Creating student profile...')
      
      const { data: studentProfile, error: studentError } = await supabase
        .from('students')
        .insert([{
          user_id: studentUser.id,
          full_name: 'John Doe Student',
          matricule: 'UBa25A1001',
          email: '<EMAIL>',
          phone_number: '+1234567890',
          department_code: 'COM',
          year_of_study: '2nd Year',
          verification_method: 'email',
          is_verified: true
        }])
        .select()

      if (studentError) {
        console.error('❌ Student profile creation failed:', studentError.message)
      } else {
        console.log('✅ Student profile created successfully')
      }
    }

    // Create department officer profile
    if (deptUser) {
      console.log('🏢 Creating department officer profile...')
      
      const { data: deptProfile, error: deptError } = await supabase
        .from('department_officers')
        .insert([{
          user_id: deptUser.id,
          full_name: 'CMC Department Officer',
          email: '<EMAIL>',
          department_code: 'CMC',
          is_active: true
        }])
        .select()

      if (deptError) {
        console.error('❌ Department officer profile creation failed:', deptError.message)
      } else {
        console.log('✅ Department officer profile created successfully')
      }
    }

    // Create admin profile
    if (adminUser) {
      console.log('👑 Creating admin profile...')
      
      const { data: adminProfile, error: adminError } = await supabase
        .from('admins')
        .insert([{
          user_id: adminUser.id,
          full_name: 'System Administrator',
          email: '<EMAIL>',
          is_super_admin: true,
          is_active: true
        }])
        .select()

      if (adminError) {
        console.error('❌ Admin profile creation failed:', adminError.message)
      } else {
        console.log('✅ Admin profile created successfully')
      }
    }

    // Test departments
    console.log('\n📊 Testing departments table...')
    const { data: departments, error: deptTableError } = await supabase
      .from('departments')
      .select('*')

    if (deptTableError) {
      console.error('❌ Failed to get departments:', deptTableError.message)
    } else {
      console.log(`✅ Departments table working: ${departments.length} departments found`)
      departments.slice(0, 3).forEach(dept => {
        console.log(`   - ${dept.name} (${dept.code})`)
      })
    }

    console.log('\n🎉 Profile creation completed!')
    console.log('\n🔑 Test Credentials:')
    console.log('👨‍🎓 Student Login (http://localhost:5175/student/login):')
    console.log('   Matricule: UBa25A1001')
    console.log('   Password: student123')
    
    console.log('\n🏢 Department Officer Login (http://localhost:5175/department/login):')
    console.log('   Email: <EMAIL>')
    console.log('   Password: dept123')
    
    console.log('\n👑 Admin Login (http://localhost:5175/admin/login):')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')

  } catch (error) {
    console.error('💥 Unexpected error:', error.message)
  }
}

// Test the complete authentication flow
async function testCompleteFlow() {
  console.log('\n🧪 Testing complete authentication flow...\n')

  // Use anon key for testing login (like the app would)
  const anonSupabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_ANON_KEY)

  try {
    // Test 1: Student matricule lookup and login
    console.log('🔍 Testing student matricule lookup...')
    
    const { data: student, error: studentError } = await anonSupabase
      .from('students')
      .select('*')
      .eq('matricule', 'UBa25A1001')
      .single()

    if (studentError) {
      console.error('❌ Failed to find student by matricule:', studentError.message)
    } else {
      console.log('✅ Found student by matricule:', student.full_name)
      
      // Test login
      const { data: loginData, error: loginError } = await anonSupabase.auth.signInWithPassword({
        email: student.email,
        password: 'student123'
      })

      if (loginError) {
        console.error('❌ Student login failed:', loginError.message)
      } else {
        console.log('✅ Student login successful!')
        
        // Test getting student profile after login
        const { data: profileData, error: profileError } = await anonSupabase
          .from('students')
          .select('*')
          .eq('user_id', loginData.user.id)
          .single()

        if (profileError) {
          console.error('❌ Failed to get student profile after login:', profileError.message)
        } else {
          console.log('✅ Student profile retrieved after login:', profileData.matricule)
        }
      }
    }

    await anonSupabase.auth.signOut()

    // Test 2: Department officer login
    console.log('\n🏢 Testing department officer login...')
    
    const { data: deptLogin, error: deptLoginError } = await anonSupabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'dept123'
    })

    if (deptLoginError) {
      console.error('❌ Department officer login failed:', deptLoginError.message)
    } else {
      console.log('✅ Department officer login successful!')
    }

    await anonSupabase.auth.signOut()

    console.log('\n🎉 Authentication flow testing completed!')
    console.log('\n✅ Ready to test in browser!')

  } catch (error) {
    console.error('💥 Flow test error:', error.message)
  }
}

// Run both functions
async function runAll() {
  await createUserProfiles()
  await testCompleteFlow()
  console.log('\n✨ All setup and testing completed!')
}

runAll().then(() => {
  process.exit(0)
}).catch((error) => {
  console.error('💥 Setup failed:', error.message)
  process.exit(1)
})
