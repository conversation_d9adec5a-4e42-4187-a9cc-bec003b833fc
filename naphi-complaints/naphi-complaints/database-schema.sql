-- NAPHI Complaints Database Schema
-- Run this in your Supabase SQL Editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('student', 'admin', 'department_officer');
CREATE TYPE complaint_status AS ENUM ('pending', 'in_progress', 'resolved', 'rejected');
CREATE TYPE complaint_category AS ENUM ('ca_mark', 'exam_mark', 'other');
CREATE TYPE verification_method AS ENUM ('email', 'phone');

-- Departments table
CREATE TABLE departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert NAPHI departments
INSERT INTO departments (code, name) VALUES
    ('CMC', 'Centre for Cybersecurity and Mathematical Cryptology'),
    ('CBE', 'Chemical and Biological Engineering'),
    ('CVL', 'Civil Engineering and Architecture'),
    ('COM', 'Computer Engineering'),
    ('EEEE', 'Electrical and Electronics Engineering'),
    ('MEC', 'Mechanical and Industrial Engineering'),
    ('MIN', 'Mining and Mineral Engineering'),
    ('PET', 'Petroleum Engineering');

-- Students table
CREATE TABLE students (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    matricule VARCHAR(20) UNIQUE NOT NULL CHECK (matricule ~ '^UBa\d{2}[A-Z]\d{4}$'),
    email TEXT UNIQUE NOT NULL,
    phone_number TEXT NOT NULL,
    department_code VARCHAR(10) REFERENCES departments(code),
    year_of_study TEXT NOT NULL CHECK (year_of_study IN ('1st Year', '2nd Year', '3rd Year', '4th Year')),
    verification_method verification_method DEFAULT 'email',
    is_verified BOOLEAN DEFAULT FALSE,
    verification_code TEXT,
    verification_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Department Officers table
CREATE TABLE department_officers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    department_code VARCHAR(10) REFERENCES departments(code),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admins table
CREATE TABLE admins (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    is_super_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Complaints table
CREATE TABLE complaints (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    department_code VARCHAR(10) REFERENCES departments(code),
    category complaint_category NOT NULL,
    course_code TEXT,
    course_title TEXT,
    course_level TEXT,
    academic_year TEXT NOT NULL,
    semester TEXT NOT NULL CHECK (semester IN ('1st Semester', '2nd Semester')),
    description TEXT NOT NULL,
    status complaint_status DEFAULT 'pending',
    response_note TEXT,
    resolved_by UUID REFERENCES department_officers(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- File attachments table
CREATE TABLE complaint_attachments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    complaint_id UUID REFERENCES complaints(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feedback table
CREATE TABLE complaint_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    complaint_id UUID REFERENCES complaints(id) ON DELETE CASCADE,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    complaint_id UUID REFERENCES complaints(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    notification_type TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System settings table
CREATE TABLE system_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_by UUID REFERENCES admins(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
    ('complaint_deadline', '30', 'Days after which complaints are automatically closed'),
    ('daily_notification_time', '17:00', 'Time to send daily summary emails to department officers'),
    ('max_file_size', '10485760', 'Maximum file size for attachments in bytes (10MB)'),
    ('allowed_file_types', 'pdf,doc,docx,jpg,jpeg,png', 'Allowed file types for attachments');

-- Create indexes for better performance
CREATE INDEX idx_students_matricule ON students(matricule);
CREATE INDEX idx_students_user_id ON students(user_id);
CREATE INDEX idx_complaints_student_id ON complaints(student_id);
CREATE INDEX idx_complaints_department ON complaints(department_code);
CREATE INDEX idx_complaints_status ON complaints(status);
CREATE INDEX idx_complaints_created_at ON complaints(created_at);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);

-- Row Level Security Policies

-- Students can only see their own data
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Students can view own profile" ON students
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Students can update own profile" ON students
    FOR UPDATE USING (auth.uid() = user_id);

-- Students can only see their own complaints
ALTER TABLE complaints ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Students can view own complaints" ON complaints
    FOR SELECT USING (
        student_id IN (
            SELECT id FROM students WHERE user_id = auth.uid()
        )
    );
CREATE POLICY "Students can create complaints" ON complaints
    FOR INSERT WITH CHECK (
        student_id IN (
            SELECT id FROM students WHERE user_id = auth.uid()
        )
    );

-- Department officers can see complaints for their department
CREATE POLICY "Department officers can view department complaints" ON complaints
    FOR SELECT USING (
        department_code IN (
            SELECT department_code FROM department_officers WHERE user_id = auth.uid()
        )
    );
CREATE POLICY "Department officers can update department complaints" ON complaints
    FOR UPDATE USING (
        department_code IN (
            SELECT department_code FROM department_officers WHERE user_id = auth.uid()
        )
    );

-- Admins can see all complaints
CREATE POLICY "Admins can view all complaints" ON complaints
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins WHERE user_id = auth.uid()
        )
    );

-- Department officers table policies
ALTER TABLE department_officers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Department officers can view own profile" ON department_officers
    FOR SELECT USING (auth.uid() = user_id);

-- Admins table policies
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Admins can view own profile" ON admins
    FOR SELECT USING (auth.uid() = user_id);

-- Departments are readable by all authenticated users
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can view departments" ON departments
    FOR SELECT USING (auth.role() = 'authenticated');

-- Notifications policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- File attachments policies
ALTER TABLE complaint_attachments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view attachments for accessible complaints" ON complaint_attachments
    FOR SELECT USING (
        complaint_id IN (
            SELECT id FROM complaints WHERE 
                student_id IN (SELECT id FROM students WHERE user_id = auth.uid())
                OR department_code IN (SELECT department_code FROM department_officers WHERE user_id = auth.uid())
                OR EXISTS (SELECT 1 FROM admins WHERE user_id = auth.uid())
        )
    );

-- Feedback policies
ALTER TABLE complaint_feedback ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Students can create feedback for own complaints" ON complaint_feedback
    FOR INSERT WITH CHECK (
        student_id IN (SELECT id FROM students WHERE user_id = auth.uid())
        AND complaint_id IN (
            SELECT id FROM complaints WHERE student_id IN (
                SELECT id FROM students WHERE user_id = auth.uid()
            )
        )
    );

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_complaints_updated_at BEFORE UPDATE ON complaints
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_department_officers_updated_at BEFORE UPDATE ON department_officers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_admins_updated_at BEFORE UPDATE ON admins
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to create notification when complaint status changes
CREATE OR REPLACE FUNCTION create_status_notification()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO notifications (user_id, complaint_id, title, message, notification_type)
        SELECT 
            s.user_id,
            NEW.id,
            'Complaint Status Updated',
            'Your complaint status has been changed to: ' || NEW.status,
            'status_update'
        FROM students s
        WHERE s.id = NEW.student_id;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER complaint_status_notification AFTER UPDATE ON complaints
    FOR EACH ROW EXECUTE FUNCTION create_status_notification();
