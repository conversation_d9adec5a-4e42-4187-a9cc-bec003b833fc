-- Fix RLS policies for student login
-- Run this in your Supabase SQL Editor

-- Drop existing policies
DROP POLICY IF EXISTS "Students can view own profile" ON students;

-- Create new policies that allow matricule lookup for login
CREATE POLICY "Students can view own profile" ON students
    FOR SELECT USING (auth.uid() = user_id);

-- Allow anonymous users to lookup students by matricule for login purposes
CREATE POLICY "Allow matricule lookup for login" ON students
    FOR SELECT USING (true);

-- Update the policy to be more specific - only allow reading email and basic info for login
DROP POLICY IF EXISTS "Allow matricule lookup for login" ON students;

CREATE POLICY "Allow matricule lookup for login" ON students
    FOR SELECT USING (
        -- Allow if user is viewing their own profile
        auth.uid() = user_id 
        OR 
        -- Allow anonymous access for login lookup (limited fields)
        auth.role() = 'anon'
    );
