{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../../src/parse.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,sEAAsE;AACtE,gEAAgE;AAChE,EAAE;AACF,gEAAgE;AAChE,qEAAqE;AACrE,sEAAsE;AACtE,EAAE;AACF,oEAAoE;AACpE,sEAAsE;AACtE,qCAAqC;AACrC,EAAE;AACF,uEAAuE;AACvE,4BAA4B;AAC5B,EAAE;AACF,uEAAuE;AACvE,kDAAkD;AAClD,EAAE;AACF,6DAA6D;AAE7D,OAAO,EAAE,YAAY,IAAI,EAAE,EAAE,MAAM,QAAQ,CAAA;AAC3C,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,UAAU,CAAA;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,SAAS,CAAA;AACjC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAEpC,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAA;AAC9B,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAC3C,OAAO,EACL,UAAU,GAGX,MAAM,kBAAkB,CAAA;AAEzB,MAAM,gBAAgB,GAAG,IAAI,GAAG,IAAI,CAAA;AACpC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;AAE5C,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC3C,MAAM,EAAE,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAA;AACnC,MAAM,GAAG,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAA;AAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC3C,MAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAA;AACjD,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AACzC,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AACzC,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC,CAAA;AAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC7B,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC,CAAA;AAC/C,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAChC,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AAEzC,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,CAAA;AAIvB,MAAM,OAAO,MAAO,SAAQ,EAAE;IAC5B,IAAI,CAAQ;IACZ,MAAM,CAAS;IACf,gBAAgB,CAAQ;IACxB,MAAM,CAA0C;IAChD,MAAM,CAAuB;IAE7B,QAAQ,GAAS,IAAI,CAAA;IACrB,QAAQ,GAAU,KAAK,CAAC;IAExB,CAAC,KAAK,CAAC,GACL,IAAI,OAAO,EAAE,CAAC;IAChB,CAAC,MAAM,CAAC,CAAU;IAClB,CAAC,SAAS,CAAC,CAAa;IACxB,CAAC,UAAU,CAAC,CAAa;IACzB,CAAC,KAAK,CAAC,GAAU,OAAO,CAAC;IACzB,CAAC,IAAI,CAAC,GAAW,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,CAAO;IACX,CAAC,GAAG,CAAC,CAAO;IACZ,CAAC,KAAK,CAAC,GAAY,KAAK,CAAC;IACzB,CAAC,KAAK,CAAC,CAAoC;IAC3C,CAAC,OAAO,CAAC,GAAY,KAAK,CAAC;IAC3B,CAAC,eAAe,CAAC,CAAW;IAC5B,CAAC,cAAc,CAAC,GAAY,KAAK,CAAC;IAClC,CAAC,OAAO,CAAC,GAAY,KAAK,CAAC;IAC3B,CAAC,OAAO,CAAC,GAAY,KAAK,CAAC;IAC3B,CAAC,SAAS,CAAC,GAAY,KAAK,CAAC;IAC7B,CAAC,UAAU,CAAC,GAAY,KAAK,CAAA;IAE7B,YAAY,MAAkB,EAAE;QAC9B,KAAK,EAAE,CAAA;QAEP,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAA;QAE1B,mEAAmE;QACnE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE;YACjB,IACE,IAAI,CAAC,KAAK,CAAC,KAAK,OAAO;gBACvB,IAAI,CAAC,eAAe,CAAC,KAAK,KAAK,EAC/B,CAAC;gBACD,iEAAiE;gBACjE,2CAA2C;gBAC3C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,6BAA6B,CAAC,CAAA;YAC7D,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;QAC3B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClB,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAA;QAC1B,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,IAAI,gBAAgB,CAAA;QAChE,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAA;QAClE,kEAAkE;QAClE,oEAAoE;QACpE,sCAAsC;QACtC,MAAM,KAAK,GACT,GAAG,CAAC,IAAI;YACR,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;QAC7D,iEAAiE;QACjE,sDAAsD;QACtD,IAAI,CAAC,MAAM;YACT,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM;gBAClD,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;oBACnB,CAAC,CAAC,KAAK,CAAA;QAET,yDAAyD;QACzD,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;QAEzC,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACrC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QACD,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YAC1C,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,CAAC,CAAA;QACnC,CAAC;IACH,CAAC;IAED,IAAI,CACF,IAAY,EACZ,OAAuB,EACvB,OAAiB,EAAE;QAEnB,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;IACvC,CAAC;IAED,CAAC,aAAa,CAAC,CAAC,KAAa,EAAE,QAAgB;QAC7C,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,GAAG,KAAK,CAAA;QAC/B,CAAC;QACD,IAAI,MAAM,CAAA;QACV,IAAI,CAAC;YACH,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;QAC3D,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAW,CAAC,CAAA;QACpD,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;gBACpB,4DAA4D;gBAC5D,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE,CAAC;oBAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;gBACxB,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI,CAAA;gBAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,CAAC,GAAG,KAAK,CAAA;YAC5B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACvB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;YAChE,CAAC;iBAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACxB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;YAChE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;gBACxB,IAAI,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACvD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,mBAAmB,EAAE;wBAClD,MAAM;qBACP,CAAC,CAAA;gBACJ,CAAC;qBAAM,IACL,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC/B,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC;oBACvC,MAAM,CAAC,QAAQ,EACf,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,oBAAoB,EAAE;wBACnD,MAAM;qBACP,CAAC,CAAA;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,SAAS,CAC7C,MAAM,EACN,IAAI,CAAC,EAAE,CAAC,EACR,IAAI,CAAC,GAAG,CAAC,CACV,CAAC,CAAA;oBAEF,8DAA8D;oBAC9D,+DAA+D;oBAC/D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;wBAC3B,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;4BACjB,yBAAyB;4BACzB,MAAM,KAAK,GAAG,GAAG,EAAE;gCACjB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oCACnB,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;gCAC9B,CAAC;4BACH,CAAC,CAAA;4BACD,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;wBACxB,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;wBAC9B,CAAC;oBACH,CAAC;oBAED,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;wBACf,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BACvC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAA;4BACnB,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;4BACjC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;4BACtB,KAAK,CAAC,MAAM,EAAE,CAAA;wBAChB,CAAC;6BAAM,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;4BAC1B,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;4BACf,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;4BACxC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAA;wBACtB,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAA;wBACpB,KAAK,CAAC,MAAM;4BACV,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;wBAEjD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;4BACjB,mDAAmD;4BACnD,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;4BACjC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAA;4BAChD,KAAK,CAAC,MAAM,EAAE,CAAA;wBAChB,CAAC;6BAAM,CAAC;4BACN,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gCACjB,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAA;4BACtB,CAAC;iCAAM,CAAC;gCACN,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;gCACtB,KAAK,CAAC,GAAG,EAAE,CAAA;4BACb,CAAC;4BAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gCACrB,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gCACvB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAA;4BACnB,CAAC;iCAAM,CAAC;gCACN,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;4BACzB,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,CAAC,WAAW,CAAC;QACX,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;IAC1C,CAAC;IAED,CAAC,YAAY,CAAC,CAAC,KAA+C;QAC5D,IAAI,EAAE,GAAG,IAAI,CAAA;QAEb,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;YAC3B,EAAE,GAAG,KAAK,CAAA;QACZ,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,GAAgC,KAAK,CAAA;YACxD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;QACxB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;YACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YACzB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBACtB,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;gBACxC,EAAE,GAAG,KAAK,CAAA;YACZ,CAAC;QACH,CAAC;QAED,OAAO,EAAE,CAAA;IACX,CAAC;IAED,CAAC,SAAS,CAAC;QACT,GAAG,CAAC,CAAA,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,EAAC;QAErD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YACxB,kEAAkE;YAClE,6CAA6C;YAC7C,wDAAwD;YACxD,kEAAkE;YAClE,qCAAqC;YACrC,kEAAkE;YAClE,2DAA2D;YAC3D,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAA;YAC1B,MAAM,QAAQ,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,MAAM,CAAA;YAC3D,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;YAC5C,CAAC;QACH,CAAC;IACH,CAAC;IAED,CAAC,WAAW,CAAC,CAAC,KAAa,EAAE,QAAgB;QAC3C,uDAAuD;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;QAC9B,qBAAqB;QACrB,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;QAC5D,CAAC;QACD,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,IAAI,CAAC,CAAA;QACjC,oBAAoB;QACpB,MAAM,CAAC,GACL,EAAE,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,KAAK,CAAC,CAAC,CAAC;YACpC,KAAK;YACP,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAA;QAE3C,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAEd,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;YACtB,IAAI,CAAC,UAAU,CAAC,GAAG,SAAS,CAAA;YAC5B,KAAK,CAAC,GAAG,EAAE,CAAA;QACb,CAAC;QAED,OAAO,CAAC,CAAC,MAAM,CAAA;IACjB,CAAC;IAED,CAAC,WAAW,CAAC,CAAC,KAAa,EAAE,QAAgB;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAE9C,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAA;QACvB,CAAC;QAED,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,EAAmB,EAAE,IAAU,EAAE,KAAW;QACjD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;QACrC,CAAC;IACH,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,KAAgB;QACzB,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,gBAAgB,CAAC;YACtB,KAAK,mBAAmB;gBACtB,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA;gBACjD,MAAK;YAEP,KAAK,sBAAsB;gBACzB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;gBAClD,MAAK;YAEP,KAAK,qBAAqB,CAAC;YAC3B,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACtB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBAC1C,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAA;gBACb,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;gBACxC,MAAK;YACP,CAAC;YAED,KAAK,yBAAyB,CAAC,CAAC,CAAC;gBAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBAC1C,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAA;gBACb,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;gBAC5C,MAAK;YACP,CAAC;YAED,qBAAqB;YACrB;gBACE,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA;YAChD,oBAAoB;QACtB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAY;QAChB,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QACzB,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAA;IACvD,CAAC;IAWD,KAAK,CACH,KAAsB,EACtB,QAAuC,EACvC,EAAc;QAEd,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,KAAK,GAAG,MAAM,CAAC,IAAI,CACjB,KAAK;YACL,oBAAoB;YACpB,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CACjD,CAAA;QACH,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAClB,oBAAoB;YACpB,EAAE,EAAE,EAAE,CAAA;YACN,OAAO,KAAK,CAAA;QACd,CAAC;QAED,gCAAgC;QAChC,MAAM,SAAS,GACb,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS;YACzB,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,CAAA;QACtD,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjB,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;gBAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;YAC1B,CAAC;YACD,IAAI,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;gBACrC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAA;gBACpB,oBAAoB;gBACpB,EAAE,EAAE,EAAE,CAAA;gBACN,OAAO,IAAI,CAAA;YACb,CAAC;YAED,uBAAuB;YACvB,KACE,IAAI,CAAC,GAAG,CAAC,EACT,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,EAClD,CAAC,EAAE,EACH,CAAC;gBACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;gBACrB,CAAC;YACH,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,CAAA;YAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,WAAW,EAAE,CAAC;gBACzC,gEAAgE;gBAChE,kEAAkE;gBAClE,kCAAkC;gBAClC,2DAA2D;gBAC3D,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;oBACvB,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;oBACpB,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAA;wBACpB,oBAAoB;wBACpB,EAAE,EAAE,EAAE,CAAA;wBACN,OAAO,IAAI,CAAA;oBACb,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,2DAA2D;oBAC3D,mCAAmC;oBACnC,IAAI,CAAC;wBACH,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;wBAClC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;oBACrB,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;oBACpB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IACE,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS;gBACzB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EACtC,CAAC;gBACD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;gBACzB,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;gBACnB,IAAI,CAAC,KAAK,CAAC;oBACT,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;wBACzB,IAAI,KAAK,CAAC,EAAE,CAAC;wBACf,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,CAAA;gBAC5B,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;gBAC1D,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAW,CAAC,CAAC,CAAA;gBACtD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACzB,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;oBAClB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAA;gBACtB,CAAC,CAAC,CAAA;gBACF,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;gBACpB,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAA;gBACzD,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAA;gBACrB,EAAE,EAAE,EAAE,CAAA;gBACN,OAAO,GAAG,CAAA;YACZ,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAA;QAErB,yEAAyE;QACzE,MAAM,GAAG,GACP,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;YAC1B,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO;gBAC3C,CAAC,CAAC,IAAI,CAAA;QAER,2DAA2D;QAC3D,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;QAC1D,CAAC;QAED,oBAAoB;QACpB,EAAE,EAAE,EAAE,CAAA;QACN,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,CAAC,YAAY,CAAC,CAAC,CAAS;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAED,CAAC,QAAQ,CAAC;QACR,IACE,IAAI,CAAC,KAAK,CAAC;YACX,CAAC,IAAI,CAAC,UAAU,CAAC;YACjB,CAAC,IAAI,CAAC,OAAO,CAAC;YACd,CAAC,IAAI,CAAC,SAAS,CAAC,EAChB,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAA;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;YAC9B,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC/B,mCAAmC;gBACnC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnD,IAAI,CAAC,IAAI,CACP,iBAAiB,EACjB,2BAA2B,KAAK,CAAC,WAAW,qBAAqB,IAAI,aAAa,EAClF,EAAE,KAAK,EAAE,CACV,CAAA;gBACD,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACjB,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC3B,CAAC;gBACD,KAAK,CAAC,GAAG,EAAE,CAAA;YACb,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAA;QAClB,CAAC;IACH,CAAC;IAED,CAAC,YAAY,CAAC,CAAC,KAAc;QAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;aAAM,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAA;QAClB,CAAC;aAAM,IAAI,KAAK,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;YACtB,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjB,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAA;gBACzB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;gBACtB,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;gBACxB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAA;YAC9B,CAAC;YAED,OACE,IAAI,CAAC,MAAM,CAAC;gBACX,IAAI,CAAC,MAAM,CAAY,EAAE,MAAM,IAAI,GAAG;gBACvC,CAAC,IAAI,CAAC,OAAO,CAAC;gBACd,CAAC,IAAI,CAAC,OAAO,CAAC,EACd,CAAC;gBACD,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;gBACtB,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;gBACxB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1B,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;QACzB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAA;QAClB,CAAC;IACH,CAAC;IAED,CAAC,eAAe,CAAC,CAAC,KAAa;QAC7B,uEAAuE;QACvE,yEAAyE;QACzE,IAAI,QAAQ,GAAG,CAAC,CAAA;QAChB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;QAC3B,OACE,QAAQ,GAAG,GAAG,IAAI,MAAM;YACxB,CAAC,IAAI,CAAC,OAAO,CAAC;YACd,CAAC,IAAI,CAAC,OAAO,CAAC,EACd,CAAC;YACD,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpB,KAAK,OAAO,CAAC;gBACb,KAAK,QAAQ;oBACX,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;oBACpC,QAAQ,IAAI,GAAG,CAAA;oBACf,MAAK;gBAEP,KAAK,QAAQ,CAAC;gBACd,KAAK,MAAM;oBACT,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;oBAC9C,MAAK;gBAEP,KAAK,MAAM;oBACT,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;oBAC9C,MAAK;gBAEP,qBAAqB;gBACrB;oBACE,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;gBAClD,oBAAoB;YACtB,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,GAAG,MAAM,EAAE,CAAC;YACtB,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjB,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC3B,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBACxB,IAAI,CAAC,MAAM,CAAC;iBACb,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAKD,GAAG,CACD,KAAsC,EACtC,QAAwC,EACxC,EAAe;QAEf,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAChC,EAAE,GAAG,KAAK,CAAA;YACV,QAAQ,GAAG,SAAS,CAAA;YACpB,KAAK,GAAG,SAAS,CAAA;QACnB,CAAC;QACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QACtC,CAAC;QACD,IAAI,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACnB,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChB,qBAAqB;gBACrB,IAAI,KAAK;oBAAE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;gBACnC,oBAAoB;gBACpB,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;gBAClB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;oBAC3B,KAAK,GAAG,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAClC,IAAI,KAAK;oBAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;gBAC5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAA;YAClB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;CACF", "sourcesContent": ["// this[BUFFER] is the remainder of a chunk if we're waiting for\n// the full 512 bytes of a header to come in.  We will Buffer.concat()\n// it to the next write(), which is a mem copy, but a small one.\n//\n// this[QUEUE] is a Yallist of entries that haven't been emitted\n// yet this can only get filled up if the user keeps write()ing after\n// a write() returns false, or does a write() with more than one entry\n//\n// We don't buffer chunks, we always parse them and either create an\n// entry, or push it into the active entry.  The ReadEntry class knows\n// to throw data away if .ignore=true\n//\n// Shift entry off the buffer when it emits 'end', and emit 'entry' for\n// the next one in the list.\n//\n// At any time, we're pushing body chunks into the entry at WRITEENTRY,\n// and waiting for 'end' on the entry at READENTRY\n//\n// ignored entries get .resume() called on them straight away\n\nimport { EventEmitter as EE } from 'events'\nimport { BrotliDecompress, Unzip } from 'minizlib'\nimport { Yallist } from 'yallist'\nimport { Header } from './header.js'\nimport { TarOptions } from './options.js'\nimport { Pax } from './pax.js'\nimport { ReadEntry } from './read-entry.js'\nimport {\n  warnMethod,\n  type WarnData,\n  type Warner,\n} from './warn-method.js'\n\nconst maxMetaEntrySize = 1024 * 1024\nconst gzipHeader = Buffer.from([0x1f, 0x8b])\n\nconst STATE = Symbol('state')\nconst WRITEENTRY = Symbol('writeEntry')\nconst READENTRY = Symbol('readEntry')\nconst NEXTENTRY = Symbol('nextEntry')\nconst PROCESSENTRY = Symbol('processEntry')\nconst EX = Symbol('extendedHeader')\nconst GEX = Symbol('globalExtendedHeader')\nconst META = Symbol('meta')\nconst EMITMETA = Symbol('emitMeta')\nconst BUFFER = Symbol('buffer')\nconst QUEUE = Symbol('queue')\nconst ENDED = Symbol('ended')\nconst EMITTEDEND = Symbol('emittedEnd')\nconst EMIT = Symbol('emit')\nconst UNZIP = Symbol('unzip')\nconst CONSUMECHUNK = Symbol('consumeChunk')\nconst CONSUMECHUNKSUB = Symbol('consumeChunkSub')\nconst CONSUMEBODY = Symbol('consumeBody')\nconst CONSUMEMETA = Symbol('consumeMeta')\nconst CONSUMEHEADER = Symbol('consumeHeader')\nconst CONSUMING = Symbol('consuming')\nconst BUFFERCONCAT = Symbol('bufferConcat')\nconst MAYBEEND = Symbol('maybeEnd')\nconst WRITING = Symbol('writing')\nconst ABORTED = Symbol('aborted')\nconst DONE = Symbol('onDone')\nconst SAW_VALID_ENTRY = Symbol('sawValidEntry')\nconst SAW_NULL_BLOCK = Symbol('sawNullBlock')\nconst SAW_EOF = Symbol('sawEOF')\nconst CLOSESTREAM = Symbol('closeStream')\n\nconst noop = () => true\n\nexport type State = 'begin' | 'header' | 'ignore' | 'meta' | 'body'\n\nexport class Parser extends EE implements Warner {\n  file: string\n  strict: boolean\n  maxMetaEntrySize: number\n  filter: Exclude<TarOptions['filter'], undefined>\n  brotli?: TarOptions['brotli']\n\n  writable: true = true\n  readable: false = false;\n\n  [QUEUE]: Yallist<ReadEntry | [string | symbol, any, any]> =\n    new Yallist();\n  [BUFFER]?: Buffer;\n  [READENTRY]?: ReadEntry;\n  [WRITEENTRY]?: ReadEntry;\n  [STATE]: State = 'begin';\n  [META]: string = '';\n  [EX]?: Pax;\n  [GEX]?: Pax;\n  [ENDED]: boolean = false;\n  [UNZIP]?: false | Unzip | BrotliDecompress;\n  [ABORTED]: boolean = false;\n  [SAW_VALID_ENTRY]?: boolean;\n  [SAW_NULL_BLOCK]: boolean = false;\n  [SAW_EOF]: boolean = false;\n  [WRITING]: boolean = false;\n  [CONSUMING]: boolean = false;\n  [EMITTEDEND]: boolean = false\n\n  constructor(opt: TarOptions = {}) {\n    super()\n\n    this.file = opt.file || ''\n\n    // these BADARCHIVE errors can't be detected early. listen on DONE.\n    this.on(DONE, () => {\n      if (\n        this[STATE] === 'begin' ||\n        this[SAW_VALID_ENTRY] === false\n      ) {\n        // either less than 1 block of data, or all entries were invalid.\n        // Either way, probably not even a tarball.\n        this.warn('TAR_BAD_ARCHIVE', 'Unrecognized archive format')\n      }\n    })\n\n    if (opt.ondone) {\n      this.on(DONE, opt.ondone)\n    } else {\n      this.on(DONE, () => {\n        this.emit('prefinish')\n        this.emit('finish')\n        this.emit('end')\n      })\n    }\n\n    this.strict = !!opt.strict\n    this.maxMetaEntrySize = opt.maxMetaEntrySize || maxMetaEntrySize\n    this.filter = typeof opt.filter === 'function' ? opt.filter : noop\n    // Unlike gzip, brotli doesn't have any magic bytes to identify it\n    // Users need to explicitly tell us they're extracting a brotli file\n    // Or we infer from the file extension\n    const isTBR =\n      opt.file &&\n      (opt.file.endsWith('.tar.br') || opt.file.endsWith('.tbr'))\n    // if it's a tbr file it MIGHT be brotli, but we don't know until\n    // we look at it and verify it's not a valid tar file.\n    this.brotli =\n      !opt.gzip && opt.brotli !== undefined ? opt.brotli\n      : isTBR ? undefined\n      : false\n\n    // have to set this so that streams are ok piping into it\n    this.on('end', () => this[CLOSESTREAM]())\n\n    if (typeof opt.onwarn === 'function') {\n      this.on('warn', opt.onwarn)\n    }\n    if (typeof opt.onReadEntry === 'function') {\n      this.on('entry', opt.onReadEntry)\n    }\n  }\n\n  warn(\n    code: string,\n    message: string | Error,\n    data: WarnData = {},\n  ): void {\n    warnMethod(this, code, message, data)\n  }\n\n  [CONSUMEHEADER](chunk: Buffer, position: number) {\n    if (this[SAW_VALID_ENTRY] === undefined) {\n      this[SAW_VALID_ENTRY] = false\n    }\n    let header\n    try {\n      header = new Header(chunk, position, this[EX], this[GEX])\n    } catch (er) {\n      return this.warn('TAR_ENTRY_INVALID', er as Error)\n    }\n\n    if (header.nullBlock) {\n      if (this[SAW_NULL_BLOCK]) {\n        this[SAW_EOF] = true\n        // ending an archive with no entries.  pointless, but legal.\n        if (this[STATE] === 'begin') {\n          this[STATE] = 'header'\n        }\n        this[EMIT]('eof')\n      } else {\n        this[SAW_NULL_BLOCK] = true\n        this[EMIT]('nullBlock')\n      }\n    } else {\n      this[SAW_NULL_BLOCK] = false\n      if (!header.cksumValid) {\n        this.warn('TAR_ENTRY_INVALID', 'checksum failure', { header })\n      } else if (!header.path) {\n        this.warn('TAR_ENTRY_INVALID', 'path is required', { header })\n      } else {\n        const type = header.type\n        if (/^(Symbolic)?Link$/.test(type) && !header.linkpath) {\n          this.warn('TAR_ENTRY_INVALID', 'linkpath required', {\n            header,\n          })\n        } else if (\n          !/^(Symbolic)?Link$/.test(type) &&\n          !/^(Global)?ExtendedHeader$/.test(type) &&\n          header.linkpath\n        ) {\n          this.warn('TAR_ENTRY_INVALID', 'linkpath forbidden', {\n            header,\n          })\n        } else {\n          const entry = (this[WRITEENTRY] = new ReadEntry(\n            header,\n            this[EX],\n            this[GEX],\n          ))\n\n          // we do this for meta & ignored entries as well, because they\n          // are still valid tar, or else we wouldn't know to ignore them\n          if (!this[SAW_VALID_ENTRY]) {\n            if (entry.remain) {\n              // this might be the one!\n              const onend = () => {\n                if (!entry.invalid) {\n                  this[SAW_VALID_ENTRY] = true\n                }\n              }\n              entry.on('end', onend)\n            } else {\n              this[SAW_VALID_ENTRY] = true\n            }\n          }\n\n          if (entry.meta) {\n            if (entry.size > this.maxMetaEntrySize) {\n              entry.ignore = true\n              this[EMIT]('ignoredEntry', entry)\n              this[STATE] = 'ignore'\n              entry.resume()\n            } else if (entry.size > 0) {\n              this[META] = ''\n              entry.on('data', c => (this[META] += c))\n              this[STATE] = 'meta'\n            }\n          } else {\n            this[EX] = undefined\n            entry.ignore =\n              entry.ignore || !this.filter(entry.path, entry)\n\n            if (entry.ignore) {\n              // probably valid, just not something we care about\n              this[EMIT]('ignoredEntry', entry)\n              this[STATE] = entry.remain ? 'ignore' : 'header'\n              entry.resume()\n            } else {\n              if (entry.remain) {\n                this[STATE] = 'body'\n              } else {\n                this[STATE] = 'header'\n                entry.end()\n              }\n\n              if (!this[READENTRY]) {\n                this[QUEUE].push(entry)\n                this[NEXTENTRY]()\n              } else {\n                this[QUEUE].push(entry)\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  [CLOSESTREAM]() {\n    queueMicrotask(() => this.emit('close'))\n  }\n\n  [PROCESSENTRY](entry?: ReadEntry | [string | symbol, any, any]) {\n    let go = true\n\n    if (!entry) {\n      this[READENTRY] = undefined\n      go = false\n    } else if (Array.isArray(entry)) {\n      const [ev, ...args]: [string | symbol, any, any] = entry\n      this.emit(ev, ...args)\n    } else {\n      this[READENTRY] = entry\n      this.emit('entry', entry)\n      if (!entry.emittedEnd) {\n        entry.on('end', () => this[NEXTENTRY]())\n        go = false\n      }\n    }\n\n    return go\n  }\n\n  [NEXTENTRY]() {\n    do {} while (this[PROCESSENTRY](this[QUEUE].shift()))\n\n    if (!this[QUEUE].length) {\n      // At this point, there's nothing in the queue, but we may have an\n      // entry which is being consumed (readEntry).\n      // If we don't, then we definitely can handle more data.\n      // If we do, and either it's flowing, or it has never had any data\n      // written to it, then it needs more.\n      // The only other possibility is that it has returned false from a\n      // write() call, so we wait for the next drain to continue.\n      const re = this[READENTRY]\n      const drainNow = !re || re.flowing || re.size === re.remain\n      if (drainNow) {\n        if (!this[WRITING]) {\n          this.emit('drain')\n        }\n      } else {\n        re.once('drain', () => this.emit('drain'))\n      }\n    }\n  }\n\n  [CONSUMEBODY](chunk: Buffer, position: number) {\n    // write up to but no  more than writeEntry.blockRemain\n    const entry = this[WRITEENTRY]\n    /* c8 ignore start */\n    if (!entry) {\n      throw new Error('attempt to consume body without entry??')\n    }\n    const br = entry.blockRemain ?? 0\n    /* c8 ignore stop */\n    const c =\n      br >= chunk.length && position === 0 ?\n        chunk\n      : chunk.subarray(position, position + br)\n\n    entry.write(c)\n\n    if (!entry.blockRemain) {\n      this[STATE] = 'header'\n      this[WRITEENTRY] = undefined\n      entry.end()\n    }\n\n    return c.length\n  }\n\n  [CONSUMEMETA](chunk: Buffer, position: number) {\n    const entry = this[WRITEENTRY]\n    const ret = this[CONSUMEBODY](chunk, position)\n\n    // if we finished, then the entry is reset\n    if (!this[WRITEENTRY] && entry) {\n      this[EMITMETA](entry)\n    }\n\n    return ret\n  }\n\n  [EMIT](ev: string | symbol, data?: any, extra?: any) {\n    if (!this[QUEUE].length && !this[READENTRY]) {\n      this.emit(ev, data, extra)\n    } else {\n      this[QUEUE].push([ev, data, extra])\n    }\n  }\n\n  [EMITMETA](entry: ReadEntry) {\n    this[EMIT]('meta', this[META])\n    switch (entry.type) {\n      case 'ExtendedHeader':\n      case 'OldExtendedHeader':\n        this[EX] = Pax.parse(this[META], this[EX], false)\n        break\n\n      case 'GlobalExtendedHeader':\n        this[GEX] = Pax.parse(this[META], this[GEX], true)\n        break\n\n      case 'NextFileHasLongPath':\n      case 'OldGnuLongPath': {\n        const ex = this[EX] ?? Object.create(null)\n        this[EX] = ex\n        ex.path = this[META].replace(/\\0.*/, '')\n        break\n      }\n\n      case 'NextFileHasLongLinkpath': {\n        const ex = this[EX] || Object.create(null)\n        this[EX] = ex\n        ex.linkpath = this[META].replace(/\\0.*/, '')\n        break\n      }\n\n      /* c8 ignore start */\n      default:\n        throw new Error('unknown meta: ' + entry.type)\n      /* c8 ignore stop */\n    }\n  }\n\n  abort(error: Error) {\n    this[ABORTED] = true\n    this.emit('abort', error)\n    // always throws, even in non-strict mode\n    this.warn('TAR_ABORT', error, { recoverable: false })\n  }\n\n  write(\n    buffer: Uint8Array | string,\n    cb?: (err?: Error | null) => void,\n  ): boolean\n  write(\n    str: string,\n    encoding?: BufferEncoding,\n    cb?: (err?: Error | null) => void,\n  ): boolean\n  write(\n    chunk: Buffer | string,\n    encoding?: BufferEncoding | (() => any),\n    cb?: () => any,\n  ): boolean {\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n    if (typeof chunk === 'string') {\n      chunk = Buffer.from(\n        chunk,\n        /* c8 ignore next */\n        typeof encoding === 'string' ? encoding : 'utf8',\n      )\n    }\n    if (this[ABORTED]) {\n      /* c8 ignore next */\n      cb?.()\n      return false\n    }\n\n    // first write, might be gzipped\n    const needSniff =\n      this[UNZIP] === undefined ||\n      (this.brotli === undefined && this[UNZIP] === false)\n    if (needSniff && chunk) {\n      if (this[BUFFER]) {\n        chunk = Buffer.concat([this[BUFFER], chunk])\n        this[BUFFER] = undefined\n      }\n      if (chunk.length < gzipHeader.length) {\n        this[BUFFER] = chunk\n        /* c8 ignore next */\n        cb?.()\n        return true\n      }\n\n      // look for gzip header\n      for (\n        let i = 0;\n        this[UNZIP] === undefined && i < gzipHeader.length;\n        i++\n      ) {\n        if (chunk[i] !== gzipHeader[i]) {\n          this[UNZIP] = false\n        }\n      }\n\n      const maybeBrotli = this.brotli === undefined\n      if (this[UNZIP] === false && maybeBrotli) {\n        // read the first header to see if it's a valid tar file. If so,\n        // we can safely assume that it's not actually brotli, despite the\n        // .tbr or .tar.br file extension.\n        // if we ended before getting a full chunk, yes, def brotli\n        if (chunk.length < 512) {\n          if (this[ENDED]) {\n            this.brotli = true\n          } else {\n            this[BUFFER] = chunk\n            /* c8 ignore next */\n            cb?.()\n            return true\n          }\n        } else {\n          // if it's tar, it's pretty reliably not brotli, chances of\n          // that happening are astronomical.\n          try {\n            new Header(chunk.subarray(0, 512))\n            this.brotli = false\n          } catch (_) {\n            this.brotli = true\n          }\n        }\n      }\n\n      if (\n        this[UNZIP] === undefined ||\n        (this[UNZIP] === false && this.brotli)\n      ) {\n        const ended = this[ENDED]\n        this[ENDED] = false\n        this[UNZIP] =\n          this[UNZIP] === undefined ?\n            new Unzip({})\n          : new BrotliDecompress({})\n        this[UNZIP].on('data', chunk => this[CONSUMECHUNK](chunk))\n        this[UNZIP].on('error', er => this.abort(er as Error))\n        this[UNZIP].on('end', () => {\n          this[ENDED] = true\n          this[CONSUMECHUNK]()\n        })\n        this[WRITING] = true\n        const ret = !!this[UNZIP][ended ? 'end' : 'write'](chunk)\n        this[WRITING] = false\n        cb?.()\n        return ret\n      }\n    }\n\n    this[WRITING] = true\n    if (this[UNZIP]) {\n      this[UNZIP].write(chunk)\n    } else {\n      this[CONSUMECHUNK](chunk)\n    }\n    this[WRITING] = false\n\n    // return false if there's a queue, or if the current entry isn't flowing\n    const ret =\n      this[QUEUE].length ? false\n      : this[READENTRY] ? this[READENTRY].flowing\n      : true\n\n    // if we have no queue, then that means a clogged READENTRY\n    if (!ret && !this[QUEUE].length) {\n      this[READENTRY]?.once('drain', () => this.emit('drain'))\n    }\n\n    /* c8 ignore next */\n    cb?.()\n    return ret\n  }\n\n  [BUFFERCONCAT](c: Buffer) {\n    if (c && !this[ABORTED]) {\n      this[BUFFER] =\n        this[BUFFER] ? Buffer.concat([this[BUFFER], c]) : c\n    }\n  }\n\n  [MAYBEEND]() {\n    if (\n      this[ENDED] &&\n      !this[EMITTEDEND] &&\n      !this[ABORTED] &&\n      !this[CONSUMING]\n    ) {\n      this[EMITTEDEND] = true\n      const entry = this[WRITEENTRY]\n      if (entry && entry.blockRemain) {\n        // truncated, likely a damaged file\n        const have = this[BUFFER] ? this[BUFFER].length : 0\n        this.warn(\n          'TAR_BAD_ARCHIVE',\n          `Truncated input (needed ${entry.blockRemain} more bytes, only ${have} available)`,\n          { entry },\n        )\n        if (this[BUFFER]) {\n          entry.write(this[BUFFER])\n        }\n        entry.end()\n      }\n      this[EMIT](DONE)\n    }\n  }\n\n  [CONSUMECHUNK](chunk?: Buffer) {\n    if (this[CONSUMING] && chunk) {\n      this[BUFFERCONCAT](chunk)\n    } else if (!chunk && !this[BUFFER]) {\n      this[MAYBEEND]()\n    } else if (chunk) {\n      this[CONSUMING] = true\n      if (this[BUFFER]) {\n        this[BUFFERCONCAT](chunk)\n        const c = this[BUFFER]\n        this[BUFFER] = undefined\n        this[CONSUMECHUNKSUB](c)\n      } else {\n        this[CONSUMECHUNKSUB](chunk)\n      }\n\n      while (\n        this[BUFFER] &&\n        (this[BUFFER] as Buffer)?.length >= 512 &&\n        !this[ABORTED] &&\n        !this[SAW_EOF]\n      ) {\n        const c = this[BUFFER]\n        this[BUFFER] = undefined\n        this[CONSUMECHUNKSUB](c)\n      }\n      this[CONSUMING] = false\n    }\n\n    if (!this[BUFFER] || this[ENDED]) {\n      this[MAYBEEND]()\n    }\n  }\n\n  [CONSUMECHUNKSUB](chunk: Buffer) {\n    // we know that we are in CONSUMING mode, so anything written goes into\n    // the buffer.  Advance the position and put any remainder in the buffer.\n    let position = 0\n    const length = chunk.length\n    while (\n      position + 512 <= length &&\n      !this[ABORTED] &&\n      !this[SAW_EOF]\n    ) {\n      switch (this[STATE]) {\n        case 'begin':\n        case 'header':\n          this[CONSUMEHEADER](chunk, position)\n          position += 512\n          break\n\n        case 'ignore':\n        case 'body':\n          position += this[CONSUMEBODY](chunk, position)\n          break\n\n        case 'meta':\n          position += this[CONSUMEMETA](chunk, position)\n          break\n\n        /* c8 ignore start */\n        default:\n          throw new Error('invalid state: ' + this[STATE])\n        /* c8 ignore stop */\n      }\n    }\n\n    if (position < length) {\n      if (this[BUFFER]) {\n        this[BUFFER] = Buffer.concat([\n          chunk.subarray(position),\n          this[BUFFER],\n        ])\n      } else {\n        this[BUFFER] = chunk.subarray(position)\n      }\n    }\n  }\n\n  end(cb?: () => void): this\n  end(data: string | Buffer, cb?: () => void): this\n  end(str: string, encoding?: BufferEncoding, cb?: () => void): this\n  end(\n    chunk?: string | Buffer | (() => void),\n    encoding?: BufferEncoding | (() => void),\n    cb?: () => void,\n  ) {\n    if (typeof chunk === 'function') {\n      cb = chunk\n      encoding = undefined\n      chunk = undefined\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n    if (typeof chunk === 'string') {\n      chunk = Buffer.from(chunk, encoding)\n    }\n    if (cb) this.once('finish', cb)\n    if (!this[ABORTED]) {\n      if (this[UNZIP]) {\n        /* c8 ignore start */\n        if (chunk) this[UNZIP].write(chunk)\n        /* c8 ignore stop */\n        this[UNZIP].end()\n      } else {\n        this[ENDED] = true\n        if (this.brotli === undefined)\n          chunk = chunk || Buffer.alloc(0)\n        if (chunk) this.write(chunk)\n        this[MAYBEEND]()\n      }\n    }\n    return this\n  }\n}\n"]}