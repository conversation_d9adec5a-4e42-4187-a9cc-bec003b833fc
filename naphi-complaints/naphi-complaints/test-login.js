import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY

// Create Supabase client (using anon key for testing login)
const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testLogin() {
  console.log('🧪 Testing NAPHI Complaints Login System...\n')

  try {
    // Test 1: Check if we can create a student profile manually
    console.log('👨‍🎓 Testing Student Profile Creation...')
    
    // First, let's try to sign in with the student account
    const { data: studentLogin, error: studentLoginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'student123'
    })

    if (studentLoginError) {
      console.error('❌ Student login failed:', studentLoginError.message)
    } else {
      console.log('✅ Student login successful:', studentLogin.user.email)
      
      // Try to create student profile
      const { data: studentProfile, error: studentProfileError } = await supabase
        .from('students')
        .insert([{
          user_id: studentLogin.user.id,
          full_name: 'John Doe Student',
          matricule: 'UBa25A1001',
          email: '<EMAIL>',
          phone_number: '+**********',
          department_code: 'COM',
          year_of_study: '2nd Year',
          verification_method: 'email',
          is_verified: true
        }])
        .select()

      if (studentProfileError) {
        console.error('❌ Student profile creation failed:', studentProfileError.message)
        
        // Check if profile already exists
        const { data: existingProfile, error: checkError } = await supabase
          .from('students')
          .select('*')
          .eq('user_id', studentLogin.user.id)
          .single()

        if (!checkError && existingProfile) {
          console.log('✅ Student profile already exists:', existingProfile.matricule)
        }
      } else {
        console.log('✅ Student profile created successfully')
      }
    }

    await supabase.auth.signOut()

    // Test 2: Test matricule-based login (our custom logic)
    console.log('\n🔍 Testing Matricule-based Login...')
    
    // Get student by matricule
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('*')
      .eq('matricule', 'UBa25A1001')
      .single()

    if (studentError) {
      console.error('❌ Failed to find student by matricule:', studentError.message)
    } else {
      console.log('✅ Found student by matricule:', student.full_name)
      
      // Now try to login with the email
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email: student.email,
        password: 'student123'
      })

      if (loginError) {
        console.error('❌ Login with email failed:', loginError.message)
      } else {
        console.log('✅ Matricule-based login flow successful!')
      }
    }

    await supabase.auth.signOut()

    // Test 3: Test Department Officer
    console.log('\n🏢 Testing Department Officer Login...')
    
    const { data: deptLogin, error: deptLoginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'dept123'
    })

    if (deptLoginError) {
      console.error('❌ Department officer login failed:', deptLoginError.message)
    } else {
      console.log('✅ Department officer login successful:', deptLogin.user.email)
      
      // Try to create department officer profile
      const { data: deptProfile, error: deptProfileError } = await supabase
        .from('department_officers')
        .insert([{
          user_id: deptLogin.user.id,
          full_name: 'CMC Department Officer',
          email: '<EMAIL>',
          department_code: 'CMC',
          is_active: true
        }])
        .select()

      if (deptProfileError) {
        console.error('❌ Department officer profile creation failed:', deptProfileError.message)
      } else {
        console.log('✅ Department officer profile created successfully')
      }
    }

    await supabase.auth.signOut()

    // Test 4: Check departments table
    console.log('\n📊 Testing Departments Table...')
    
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select('*')

    if (deptError) {
      console.error('❌ Failed to get departments:', deptError.message)
    } else {
      console.log('✅ Departments table working:', departments.length, 'departments found')
      departments.slice(0, 3).forEach(dept => {
        console.log(`   - ${dept.name} (${dept.code})`)
      })
    }

    console.log('\n🎉 Testing completed!')
    console.log('\n📋 Ready to Test in Browser:')
    console.log('🌐 Application URL: http://localhost:5175/')
    
    console.log('\n🔑 Test Credentials:')
    console.log('👨‍🎓 Student Login (http://localhost:5175/student/login):')
    console.log('   Matricule: UBa25A1001')
    console.log('   Password: student123')
    
    console.log('\n🏢 Department Officer Login (http://localhost:5175/department/login):')
    console.log('   Email: <EMAIL>')
    console.log('   Password: dept123')
    
    console.log('\n📝 Next Steps:')
    console.log('1. Open http://localhost:5175/ in your browser')
    console.log('2. Test student login with matricule UBa25A1001')
    console.log('3. Test department <NAME_EMAIL>')
    console.log('4. Try accessing admin route: http://localhost:5175/admin/login')

  } catch (error) {
    console.error('💥 Unexpected error:', error.message)
  }
}

// Run the test
testLogin().then(() => {
  console.log('\n✨ Test completed!')
  process.exit(0)
}).catch((error) => {
  console.error('💥 Test failed:', error.message)
  process.exit(1)
})
