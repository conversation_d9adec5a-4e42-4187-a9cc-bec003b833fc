import React from 'react'
import { Link } from 'react-router-dom'
import { FileText, Users, Shield, MessageSquare } from 'lucide-react'

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-naphi-blue to-naphi-dark">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-naphi-blue rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">NC</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-naphi-blue">NAPHI Complaints</h1>
                <p className="text-sm text-gray-600">Student Complaint Management System</p>
              </div>
            </div>
            <nav className="hidden md:flex space-x-4">
              <Link 
                to="/student/login" 
                className="text-naphi-blue hover:text-naphi-light font-medium"
              >
                Student Login
              </Link>
              <Link 
                to="/student/register" 
                className="btn-primary"
              >
                Register
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center text-white">
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            Welcome to NAPHI
          </h2>
          <p className="text-xl md:text-2xl mb-8 text-blue-100">
            Student Complaint Management System
          </p>
          <p className="text-lg mb-12 text-blue-200 max-w-3xl mx-auto">
            Submit, track, and resolve academic complaints efficiently. 
            Our system ensures your concerns are heard and addressed promptly by the appropriate departments.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/student/register" 
              className="bg-white text-naphi-blue hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Get Started
            </Link>
            <Link 
              to="/student/login" 
              className="border-2 border-white text-white hover:bg-white hover:text-naphi-blue font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Student Login
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-24 grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center text-white">
            <FileText className="w-12 h-12 mx-auto mb-4 text-blue-200" />
            <h3 className="text-xl font-semibold mb-2">Submit Complaints</h3>
            <p className="text-blue-200">
              Easily submit academic complaints with detailed descriptions and file attachments.
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center text-white">
            <MessageSquare className="w-12 h-12 mx-auto mb-4 text-blue-200" />
            <h3 className="text-xl font-semibold mb-2">Track Progress</h3>
            <p className="text-blue-200">
              Monitor your complaint status in real-time and receive updates via email.
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center text-white">
            <Users className="w-12 h-12 mx-auto mb-4 text-blue-200" />
            <h3 className="text-xl font-semibold mb-2">Department Response</h3>
            <p className="text-blue-200">
              Get direct responses from relevant department officers and staff.
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center text-white">
            <Shield className="w-12 h-12 mx-auto mb-4 text-blue-200" />
            <h3 className="text-xl font-semibold mb-2">Secure & Confidential</h3>
            <p className="text-blue-200">
              Your complaints are handled securely with complete confidentiality.
            </p>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden mt-16 flex flex-col space-y-4">
          <Link 
            to="/student/login" 
            className="bg-white text-naphi-blue hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg text-center transition-colors duration-200"
          >
            Student Login
          </Link>
          <Link 
            to="/student/register" 
            className="border-2 border-white text-white hover:bg-white hover:text-naphi-blue font-semibold py-3 px-6 rounded-lg text-center transition-colors duration-200"
          >
            Register as Student
          </Link>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white/10 backdrop-blur-sm mt-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-white">
            <p className="text-blue-200">
              © 2024 NAPHI - National Academy of Professional Health and Innovation
            </p>
            <p className="text-sm text-blue-300 mt-2">
              Complaint Management System
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default LandingPage
