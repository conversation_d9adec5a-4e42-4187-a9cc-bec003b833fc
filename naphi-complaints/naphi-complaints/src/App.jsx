import React from 'react'
import { <PERSON>rowser<PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import LandingPage from './components/LandingPage'
import StudentLogin from './components/auth/StudentLogin'
import AdminLogin from './components/auth/AdminLogin'
import DepartmentLogin from './components/auth/DepartmentLogin'
import StudentRegister from './components/auth/StudentRegister'
import StudentDashboard from './components/student/StudentDashboard'
import AdminDashboard from './components/admin/AdminDashboard'
import DepartmentDashboard from './components/department/DepartmentDashboard'
import ProtectedRoute from './components/shared/ProtectedRoute'

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<LandingPage />} />
            <Route path="/student/login" element={<StudentLogin />} />
            <Route path="/student/register" element={<StudentRegister />} />

            {/* Special Routes (no visible navigation) */}
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="/department/login" element={<DepartmentLogin />} />

            {/* Protected Routes */}
            <Route
              path="/student/dashboard"
              element={
                <ProtectedRoute requiredRole="student">
                  <StudentDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute requiredRole="admin">
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/department/dashboard"
              element={
                <ProtectedRoute requiredRole="department_officer">
                  <DepartmentDashboard />
                </ProtectedRoute>
              }
            />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App
