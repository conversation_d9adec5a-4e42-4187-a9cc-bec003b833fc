import { createClient } from '@supabase/supabase-js'

// These will be replaced with your actual Supabase credentials
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'YOUR_SUPABASE_URL'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'YOUR_SUPABASE_ANON_KEY'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper functions for authentication
export const auth = {
  // Sign up a new user
  signUp: async (email, password, userData) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    return { data, error }
  },

  // Sign in user
  signIn: async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  // Sign out user
  signOut: async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Get current user
  getCurrentUser: async () => {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  },

  // Listen to auth changes
  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// Helper functions for database operations
export const db = {
  // Students
  students: {
    create: async (studentData) => {
      const { data, error } = await supabase
        .from('students')
        .insert([studentData])
        .select()
      return { data, error }
    },

    getByMatricule: async (matricule) => {
      const { data, error } = await supabase
        .from('students')
        .select('*')
        .eq('matricule', matricule)
        .single()
      return { data, error }
    },

    getByUserId: async (userId) => {
      const { data, error } = await supabase
        .from('students')
        .select('*')
        .eq('user_id', userId)
        .single()
      return { data, error }
    }
  },

  // Complaints
  complaints: {
    create: async (complaintData) => {
      const { data, error } = await supabase
        .from('complaints')
        .insert([complaintData])
        .select()
      return { data, error }
    },

    getByStudentId: async (studentId) => {
      const { data, error } = await supabase
        .from('complaints')
        .select(`
          *,
          students (full_name, matricule),
          departments (name, code)
        `)
        .eq('student_id', studentId)
        .order('created_at', { ascending: false })
      return { data, error }
    },

    getByDepartment: async (departmentCode) => {
      const { data, error } = await supabase
        .from('complaints')
        .select(`
          *,
          students (full_name, matricule, email, phone_number)
        `)
        .eq('department_code', departmentCode)
        .order('created_at', { ascending: false })
      return { data, error }
    },

    getAll: async () => {
      const { data, error } = await supabase
        .from('complaints')
        .select(`
          *,
          students (full_name, matricule, email),
          departments (name, code)
        `)
        .order('created_at', { ascending: false })
      return { data, error }
    },

    updateStatus: async (complaintId, status, responseNote = null) => {
      const updateData = { 
        status,
        updated_at: new Date().toISOString()
      }
      
      if (responseNote) {
        updateData.response_note = responseNote
      }

      const { data, error } = await supabase
        .from('complaints')
        .update(updateData)
        .eq('id', complaintId)
        .select()
      return { data, error }
    }
  },

  // Departments
  departments: {
    getAll: async () => {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name')
      return { data, error }
    },

    getByCode: async (code) => {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .eq('code', code)
        .single()
      return { data, error }
    }
  },

  // Department Officers
  departmentOfficers: {
    getByUserId: async (userId) => {
      const { data, error } = await supabase
        .from('department_officers')
        .select(`
          *,
          departments (name, code)
        `)
        .eq('user_id', userId)
        .single()
      return { data, error }
    },

    getByDepartment: async (departmentCode) => {
      const { data, error } = await supabase
        .from('department_officers')
        .select('*')
        .eq('department_code', departmentCode)
      return { data, error }
    }
  }
}
