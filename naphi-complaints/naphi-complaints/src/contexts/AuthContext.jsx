import React, { createContext, useContext, useEffect, useState } from 'react'
import { auth, db } from '../utils/supabase'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [userProfile, setUserProfile] = useState(null)
  const [userRole, setUserRole] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const currentUser = await auth.getCurrentUser()
      if (currentUser) {
        await loadUserProfile(currentUser)
      }
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        await loadUserProfile(session.user)
      } else {
        setUser(null)
        setUserProfile(null)
        setUserRole(null)
      }
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserProfile = async (authUser) => {
    setUser(authUser)
    
    try {
      // Try to find user in students table
      const { data: studentData, error: studentError } = await db.students.getByUserId(authUser.id)
      if (studentData && !studentError) {
        setUserProfile(studentData)
        setUserRole('student')
        return
      }

      // Try to find user in department_officers table
      const { data: officerData, error: officerError } = await db.departmentOfficers.getByUserId(authUser.id)
      if (officerData && !officerError) {
        setUserProfile(officerData)
        setUserRole('department_officer')
        return
      }

      // Try to find user in admins table (we'll need to create this function)
      // For now, we'll check if user metadata indicates admin
      if (authUser.user_metadata?.user_type === 'admin') {
        setUserProfile({ 
          full_name: authUser.user_metadata?.full_name || authUser.email,
          email: authUser.email 
        })
        setUserRole('admin')
        return
      }

      // If no profile found, user might be unverified or have issues
      console.warn('No profile found for user:', authUser.id)
      
    } catch (error) {
      console.error('Error loading user profile:', error)
    }
  }

  const signOut = async () => {
    try {
      await auth.signOut()
      setUser(null)
      setUserProfile(null)
      setUserRole(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  }

  const value = {
    user,
    userProfile,
    userRole,
    loading,
    signOut,
    isStudent: userRole === 'student',
    isDepartmentOfficer: userRole === 'department_officer',
    isAdmin: userRole === 'admin',
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
