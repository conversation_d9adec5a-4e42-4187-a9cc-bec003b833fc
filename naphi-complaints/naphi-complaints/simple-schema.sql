-- NAPH<PERSON> Complaints - Simple Database Schema
-- Run this in your Supabase SQL Editor

-- <PERSON>reate custom types
CREATE TYPE complaint_status AS ENUM ('pending', 'in_progress', 'resolved', 'rejected');
CREATE TYPE complaint_category AS ENUM ('ca_mark', 'exam_mark', 'other');
CREATE TYPE verification_method AS ENUM ('email', 'phone');

-- Departments table
CREATE TABLE departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert NAPHI departments
INSERT INTO departments (code, name) VALUES
    ('CMC', 'Centre for Cybersecurity and Mathematical Cryptology'),
    ('CBE', 'Chemical and Biological Engineering'),
    ('CVL', 'Civil Engineering and Architecture'),
    ('COM', 'Computer Engineering'),
    ('EEEE', 'Electrical and Electronics Engineering'),
    ('MEC', 'Mechanical and Industrial Engineering'),
    ('MIN', 'Mining and Mineral Engineering'),
    ('PET', 'Petroleum Engineering');

-- Students table
CREATE TABLE students (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    matricule VARCHAR(20) UNIQUE NOT NULL CHECK (matricule ~ '^UBa\d{2}[A-Z]\d{4}$'),
    email TEXT UNIQUE NOT NULL,
    phone_number TEXT NOT NULL,
    department_code VARCHAR(10) REFERENCES departments(code),
    year_of_study TEXT NOT NULL CHECK (year_of_study IN ('1st Year', '2nd Year', '3rd Year', '4th Year')),
    verification_method verification_method DEFAULT 'email',
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Department Officers table
CREATE TABLE department_officers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    department_code VARCHAR(10) REFERENCES departments(code),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admins table
CREATE TABLE admins (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    is_super_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Complaints table
CREATE TABLE complaints (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    department_code VARCHAR(10) REFERENCES departments(code),
    category complaint_category NOT NULL,
    course_code TEXT,
    course_title TEXT,
    course_level TEXT,
    academic_year TEXT NOT NULL,
    semester TEXT NOT NULL CHECK (semester IN ('1st Semester', '2nd Semester')),
    description TEXT NOT NULL,
    status complaint_status DEFAULT 'pending',
    response_note TEXT,
    resolved_by UUID REFERENCES department_officers(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE complaints ENABLE ROW LEVEL SECURITY;
ALTER TABLE department_officers ENABLE ROW LEVEL SECURITY;
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;

-- Basic RLS Policies
CREATE POLICY "Students can view own profile" ON students
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Students can view own complaints" ON complaints
    FOR SELECT USING (
        student_id IN (
            SELECT id FROM students WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Students can create complaints" ON complaints
    FOR INSERT WITH CHECK (
        student_id IN (
            SELECT id FROM students WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Department officers can view own profile" ON department_officers
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view own profile" ON admins
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can view departments" ON departments
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create indexes
CREATE INDEX idx_students_matricule ON students(matricule);
CREATE INDEX idx_students_user_id ON students(user_id);
CREATE INDEX idx_complaints_student_id ON complaints(student_id);
CREATE INDEX idx_complaints_department ON complaints(department_code);
CREATE INDEX idx_complaints_status ON complaints(status);
