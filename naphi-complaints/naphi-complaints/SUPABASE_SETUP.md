# NAPH<PERSON> Complaints - Supabase Setup Guide

## Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in to your account
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: NAPH<PERSON> Complaints
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Choose closest to your location
5. Click "Create new project"
6. Wait for the project to be created (2-3 minutes)

## Step 2: Get Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project-id.supabase.co`)
   - **anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## Step 3: Configure Environment Variables

1. In your project folder, copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and replace the placeholder values:
   ```
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your_anon_key_here
   ```

## Step 4: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy the entire content from `database-schema.sql` file
4. Paste it into the SQL editor
5. Click "Run" to execute the schema

This will create:
- All necessary tables (students, complaints, departments, etc.)
- Row Level Security policies
- Indexes for performance
- Triggers for automatic updates
- Default department data

## Step 5: Configure Authentication

1. Go to **Authentication** → **Settings**
2. Under **Site URL**, add your development URL: `http://localhost:5174`
3. Under **Redirect URLs**, add: `http://localhost:5174/**`
4. Enable **Email confirmations** if you want email verification
5. Configure **Email templates** (optional)

## Step 6: Set Up Storage (for file attachments)

1. Go to **Storage**
2. Click "Create bucket"
3. Name it: `complaint-attachments`
4. Make it **Public** (we'll handle security with RLS)
5. Click "Create bucket"

## Step 7: Test the Connection

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Open `http://localhost:5174`
3. Try registering a new student account
4. Check if the data appears in your Supabase dashboard under **Table Editor**

## Important Notes

### Student Matricule Format
- Must follow pattern: `UBa25A1002`
- Format: `UBa` + `year` + `letter` + `4 digits`
- Example: `UBa25A1002`, `UBa24B2345`, `UBa23C0001`

### User Roles
- **Students**: Register through the app
- **Department Officers**: Must be created manually in the database
- **Admins**: Must be created manually in the database

### Creating Department Officers (Manual Process)
1. Go to **Table Editor** → **department_officers**
2. Click "Insert row"
3. Fill in:
   - `user_id`: Get from auth.users table after they sign up
   - `full_name`: Officer's full name
   - `email`: Officer's email
   - `department_code`: One of (CMC, CBE, CVL, COM, EEEE, MEC, MIN, PET)

### Creating Admins (Manual Process)
1. Go to **Table Editor** → **admins**
2. Click "Insert row"
3. Fill in:
   - `user_id`: Get from auth.users table after they sign up
   - `full_name`: Admin's full name
   - `email`: Admin's email
   - `is_super_admin`: true/false

## Troubleshooting

### Common Issues:

1. **"Invalid API key"**: Check your environment variables
2. **"Row Level Security"**: Make sure RLS policies are applied
3. **"Permission denied"**: Check if user has correct role in database
4. **"Matricule format error"**: Ensure format is exactly `UBa##X####`

### Checking Logs:
- Go to **Logs** in Supabase dashboard to see database errors
- Check browser console for JavaScript errors

## Next Steps

After completing this setup:
1. Test student registration and login
2. Create test department officers and admins
3. Test complaint submission
4. Test the complete workflow

## Security Notes

- Never commit your `.env` file to version control
- Use strong passwords for database
- Regularly update your Supabase project
- Monitor usage in the Supabase dashboard
