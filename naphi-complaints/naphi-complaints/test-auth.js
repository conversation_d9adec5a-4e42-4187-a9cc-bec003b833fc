import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuthentication() {
  console.log('🧪 Testing NAPHI Complaints Authentication System...\n')

  try {
    // Test 1: Create Student User
    console.log('👨‍🎓 Testing Student Registration...')
    
    const { data: studentAuth, error: studentAuthError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'student123',
      options: {
        data: {
          full_name: 'John <PERSON>',
          user_type: 'student'
        }
      }
    })

    if (studentAuthError) {
      console.error('❌ Student registration failed:', studentAuthError.message)
    } else {
      console.log('✅ Student auth user created:', studentAuth.user?.email)
      
      // Create student profile
      const { data: studentProfile, error: studentProfileError } = await supabase
        .from('students')
        .insert([{
          user_id: studentAuth.user.id,
          full_name: 'John Doe Student',
          matricule: 'UBa25A1001',
          email: '<EMAIL>',
          phone_number: '+1234567890',
          department_code: 'COM',
          year_of_study: '2nd Year',
          verification_method: 'email',
          is_verified: true
        }])
        .select()

      if (studentProfileError) {
        console.error('❌ Student profile creation failed:', studentProfileError.message)
      } else {
        console.log('✅ Student profile created successfully')
      }
    }

    // Sign out before next test
    await supabase.auth.signOut()

    // Test 2: Test Student Login
    console.log('\n🔐 Testing Student Login...')
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'student123'
    })

    if (loginError) {
      console.error('❌ Student login failed:', loginError.message)
    } else {
      console.log('✅ Student login successful:', loginData.user.email)
      
      // Test getting student profile
      const { data: studentData, error: studentDataError } = await supabase
        .from('students')
        .select('*')
        .eq('user_id', loginData.user.id)
        .single()

      if (studentDataError) {
        console.error('❌ Failed to get student profile:', studentDataError.message)
      } else {
        console.log('✅ Student profile retrieved:', studentData.matricule)
      }
    }

    // Sign out
    await supabase.auth.signOut()

    // Test 3: Test Database Tables
    console.log('\n📊 Testing Database Tables...')
    
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select('*')
      .limit(3)

    if (deptError) {
      console.error('❌ Failed to get departments:', deptError.message)
    } else {
      console.log('✅ Departments table working:', departments.length, 'departments found')
      departments.forEach(dept => {
        console.log(`   - ${dept.name} (${dept.code})`)
      })
    }

    console.log('\n🎉 Authentication testing completed!')
    console.log('\n📋 Test Results Summary:')
    console.log('✅ Student registration: Working')
    console.log('✅ Student login: Working')
    console.log('✅ Database connection: Working')
    console.log('✅ Student profile creation: Working')
    
    console.log('\n🔑 Test Credentials Created:')
    console.log('👨‍🎓 Student Login:')
    console.log('   Matricule: UBa25A1001')
    console.log('   Email: <EMAIL>')
    console.log('   Password: student123')
    console.log('   URL: http://localhost:5175/student/login')
    
    console.log('\n🌐 Test the application:')
    console.log('1. Go to: http://localhost:5175/student/login')
    console.log('2. Enter matricule: UBa25A1001')
    console.log('3. Enter password: student123')
    console.log('4. Click "Sign In"')

  } catch (error) {
    console.error('💥 Unexpected error:', error.message)
  }
}

// Test matricule lookup function
async function testMatriculeLogin() {
  console.log('\n🔍 Testing Matricule-based Login...')
  
  try {
    // Get student by matricule
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('*')
      .eq('matricule', 'UBa25A1001')
      .single()

    if (studentError) {
      console.error('❌ Failed to find student by matricule:', studentError.message)
      return
    }

    console.log('✅ Found student by matricule:', student.full_name)
    console.log('   Email:', student.email)
    console.log('   Department:', student.department_code)
    
    // Test login with email
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: student.email,
      password: 'student123'
    })

    if (loginError) {
      console.error('❌ Login failed:', loginError.message)
    } else {
      console.log('✅ Matricule-based login successful!')
    }

    await supabase.auth.signOut()

  } catch (error) {
    console.error('💥 Matricule login test failed:', error.message)
  }
}

// Run the tests
async function runAllTests() {
  await testAuthentication()
  await testMatriculeLogin()
  console.log('\n✨ All tests completed!')
}

runAllTests().then(() => {
  process.exit(0)
}).catch((error) => {
  console.error('💥 Tests failed:', error.message)
  process.exit(1)
})
